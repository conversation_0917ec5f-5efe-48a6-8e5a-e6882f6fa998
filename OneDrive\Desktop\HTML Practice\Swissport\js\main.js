// Main JavaScript for Swissport 3D Website
class SwissportMain {
    constructor() {
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupScrollEffects();
        this.setupSmoothScrolling();
        this.setupParallaxEffects();
        this.createServiceSections();
    }

    setupNavigation() {
        const nav = document.querySelector('.nav-container');
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            // Hide/show navigation based on scroll direction
            if (currentScrollY > lastScrollY && currentScrollY > 100) {
                nav.style.transform = 'translateY(-100%)';
            } else {
                nav.style.transform = 'translateY(0)';
            }
            
            // Change navigation background opacity
            if (currentScrollY > 50) {
                nav.style.background = 'rgba(0, 0, 0, 0.95)';
            } else {
                nav.style.background = 'rgba(0, 0, 0, 0.9)';
            }
            
            lastScrollY = currentScrollY;
        });

        // Active navigation highlighting
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-item a');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.scrollY >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    }

    setupScrollEffects() {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });
    }

    setupSmoothScrolling() {
        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupParallaxEffects() {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');
            
            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        });
    }

    createServiceSections() {
        // Create services section after hero
        const servicesHTML = `
            <section id="services" class="services-section">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title">Our Services</h2>
                        <p class="section-subtitle">Comprehensive aviation solutions worldwide</p>
                    </div>
                    
                    <div class="services-grid">
                        <div class="service-card" data-service="ground-handling">
                            <div class="service-icon">
                                <div class="icon-3d ground-handling-icon"></div>
                            </div>
                            <h3>Ground Handling</h3>
                            <p>Complete aircraft ground support services including baggage handling, aircraft cleaning, and ramp services.</p>
                            <button class="service-btn">Explore 3D Demo</button>
                        </div>
                        
                        <div class="service-card" data-service="cargo">
                            <div class="service-icon">
                                <div class="icon-3d cargo-icon"></div>
                            </div>
                            <h3>Cargo Services</h3>
                            <p>End-to-end cargo handling solutions with advanced tracking and logistics management.</p>
                            <button class="service-btn">Explore 3D Demo</button>
                        </div>
                        
                        <div class="service-card" data-service="passenger">
                            <div class="service-icon">
                                <div class="icon-3d passenger-icon"></div>
                            </div>
                            <h3>Passenger Services</h3>
                            <p>Premium passenger experience services from check-in to boarding and beyond.</p>
                            <button class="service-btn">Explore 3D Demo</button>
                        </div>
                        
                        <div class="service-card" data-service="fueling">
                            <div class="service-icon">
                                <div class="icon-3d fueling-icon"></div>
                            </div>
                            <h3>Aircraft Fueling</h3>
                            <p>Safe and efficient aircraft refueling services with environmental compliance.</p>
                            <button class="service-btn">Explore 3D Demo</button>
                        </div>
                    </div>
                </div>
            </section>

            <section id="locations" class="locations-section">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title">Global Network</h2>
                        <p class="section-subtitle">Serving airports across 6 continents</p>
                    </div>
                    
                    <div class="locations-stats">
                        <div class="stat-card">
                            <div class="stat-number">300+</div>
                            <div class="stat-label">Airports Served</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">Countries</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">66,000+</div>
                            <div class="stat-label">Employees</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">282M</div>
                            <div class="stat-label">Passengers Annually</div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="about" class="about-section">
                <div class="container">
                    <div class="about-content">
                        <div class="about-text">
                            <h2 class="section-title">About Swissport</h2>
                            <p>As the world's leading provider of ground services and air cargo handling, Swissport International AG serves over 300 airports in 50 countries across six continents.</p>
                            <p>Our commitment to safety, efficiency, and innovation drives everything we do, ensuring seamless operations for airlines and exceptional experiences for passengers worldwide.</p>
                            
                            <div class="about-features">
                                <div class="feature-item">
                                    <h4>Safety First</h4>
                                    <p>Uncompromising commitment to safety standards and protocols</p>
                                </div>
                                <div class="feature-item">
                                    <h4>Innovation</h4>
                                    <p>Cutting-edge technology and sustainable solutions</p>
                                </div>
                                <div class="feature-item">
                                    <h4>Excellence</h4>
                                    <p>Delivering exceptional service quality consistently</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="about-visual">
                            <div class="about-3d-container">
                                <canvas id="about-canvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="careers" class="careers-section">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title">Join Our Team</h2>
                        <p class="section-subtitle">Build your career in aviation with Swissport</p>
                    </div>
                    
                    <div class="careers-content">
                        <div class="career-opportunities">
                            <div class="opportunity-card">
                                <h3>Ground Operations</h3>
                                <p>Join our ground handling teams at airports worldwide</p>
                                <a href="#" class="career-link">View Positions</a>
                            </div>
                            <div class="opportunity-card">
                                <h3>Cargo & Logistics</h3>
                                <p>Shape the future of air cargo handling</p>
                                <a href="#" class="career-link">View Positions</a>
                            </div>
                            <div class="opportunity-card">
                                <h3>Technology & Innovation</h3>
                                <p>Drive digital transformation in aviation</p>
                                <a href="#" class="career-link">View Positions</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="contact" class="contact-section">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title">Get In Touch</h2>
                        <p class="section-subtitle">Connect with our global team</p>
                    </div>
                    
                    <div class="contact-content">
                        <div class="contact-info">
                            <div class="contact-item">
                                <h4>Global Headquarters</h4>
                                <p>Swissport International AG<br>
                                The Circle 31<br>
                                8058 Zurich Airport<br>
                                Switzerland</p>
                            </div>
                            <div class="contact-item">
                                <h4>Contact Information</h4>
                                <p>Phone: +41 43 815 00 00<br>
                                Email: <EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-form">
                            <form id="contactForm">
                                <div class="form-group">
                                    <input type="text" id="name" name="name" placeholder="Your Name" required>
                                </div>
                                <div class="form-group">
                                    <input type="email" id="email" name="email" placeholder="Your Email" required>
                                </div>
                                <div class="form-group">
                                    <select id="subject" name="subject" required>
                                        <option value="">Select Subject</option>
                                        <option value="services">Services Inquiry</option>
                                        <option value="careers">Career Opportunities</option>
                                        <option value="partnership">Partnership</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                                </div>
                                <button type="submit" class="submit-btn">Send Message</button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        `;

        // Insert sections after hero
        const heroSection = document.getElementById('home');
        heroSection.insertAdjacentHTML('afterend', servicesHTML);

        // Add event listeners for service demos
        this.setupServiceDemos();
        this.setupContactForm();
    }

    setupServiceDemos() {
        const serviceBtns = document.querySelectorAll('.service-btn');
        serviceBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const serviceCard = e.target.closest('.service-card');
                const serviceType = serviceCard.dataset.service;
                this.showServiceDemo(serviceType);
            });
        });
    }

    showServiceDemo(serviceType) {
        // Create modal for 3D service demonstration
        const modal = document.createElement('div');
        modal.className = 'service-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>3D Service Demonstration: ${serviceType.replace('-', ' ').toUpperCase()}</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <canvas id="service-canvas-${serviceType}"></canvas>
                    <div class="service-info">
                        <p>Interactive 3D demonstration of our ${serviceType.replace('-', ' ')} services.</p>
                        <p>Use mouse to rotate and zoom the 3D scene.</p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close modal functionality
        const closeBtn = modal.querySelector('.close-modal');
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        // Initialize 3D scene for service demo
        this.initServiceDemo(serviceType);
    }

    initServiceDemo(serviceType) {
        // This would initialize a specific 3D scene for each service type
        // For now, we'll show a placeholder
        console.log(`Initializing 3D demo for ${serviceType}`);
    }

    setupContactForm() {
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(contactForm);
                const data = Object.fromEntries(formData);
                
                // Simulate form submission
                this.submitContactForm(data);
            });
        }
    }

    submitContactForm(data) {
        // Show loading state
        const submitBtn = document.querySelector('.submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            alert('Thank you for your message! We will get back to you soon.');
            document.getElementById('contactForm').reset();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
}

// Initialize main functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SwissportMain();
});

// Add CSS for new sections
const additionalCSS = `
    .services-section,
    .locations-section,
    .about-section,
    .careers-section,
    .contact-section {
        padding: 5rem 0;
        min-height: 100vh;
    }

    .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .section-header {
        text-align: center;
        margin-bottom: 4rem;
    }

    .section-title {
        font-size: 3rem;
        font-weight: 200;
        margin-bottom: 1rem;
        color: var(--swissport-white);
    }

    .section-subtitle {
        font-size: 1.2rem;
        color: var(--swissport-red);
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .service-card {
        background: rgba(255, 255, 255, 0.05);
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .service-card:hover {
        transform: translateY(-10px);
        background: rgba(255, 255, 255, 0.1);
        box-shadow: 0 20px 40px rgba(215, 15, 10, 0.2);
    }

    .service-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
        background: var(--swissport-red);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .service-btn {
        background: var(--swissport-red);
        color: white;
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 1rem;
    }

    .service-btn:hover {
        background: #b50d08;
        transform: translateY(-2px);
    }

    .locations-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .stat-card {
        text-align: center;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--swissport-red);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .service-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    }

    .modal-content {
        background: var(--swissport-black);
        border-radius: 10px;
        max-width: 90%;
        max-height: 90%;
        overflow: hidden;
    }

    .modal-header {
        padding: 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .close-modal {
        background: none;
        border: none;
        color: white;
        font-size: 2rem;
        cursor: pointer;
    }

    @media (max-width: 768px) {
        .section-title {
            font-size: 2rem;
        }
        
        .services-grid {
            grid-template-columns: 1fr;
        }
        
        .locations-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);
