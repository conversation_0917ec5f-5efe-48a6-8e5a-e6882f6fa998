/* 3D Components and Animations for Swissport */

/* 3D Service Icons */
.icon-3d {
    width: 50px;
    height: 50px;
    position: relative;
    transform-style: preserve-3d;
    animation: rotate3d 4s infinite linear;
}

.ground-handling-icon {
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    border-radius: 10px;
    position: relative;
}

.ground-handling-icon::before {
    content: '✈';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: var(--swissport-red);
}

.cargo-icon {
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    border-radius: 5px;
    position: relative;
}

.cargo-icon::before {
    content: '📦';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
}

.passenger-icon {
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    border-radius: 50%;
    position: relative;
}

.passenger-icon::before {
    content: '👥';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
}

.fueling-icon {
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    border-radius: 10px;
    position: relative;
}

.fueling-icon::before {
    content: '⛽';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
}

@keyframes rotate3d {
    0% {
        transform: rotateY(0deg) rotateX(0deg);
    }
    25% {
        transform: rotateY(90deg) rotateX(0deg);
    }
    50% {
        transform: rotateY(180deg) rotateX(0deg);
    }
    75% {
        transform: rotateY(270deg) rotateX(0deg);
    }
    100% {
        transform: rotateY(360deg) rotateX(0deg);
    }
}

/* 3D Card Effects */
.service-card {
    transform-style: preserve-3d;
    perspective: 1000px;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

.service-card:hover {
    transform: translateY(-20px) rotateX(5deg) rotateY(5deg);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(215, 15, 10, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 50%, 
        rgba(215, 15, 10, 0.1) 100%);
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.service-card:hover::before {
    opacity: 1;
}

/* 3D Button Effects */
.cta-button,
.service-btn,
.submit-btn {
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
}

.cta-button::before,
.service-btn::before,
.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.2) 0%, 
        rgba(255, 255, 255, 0.1) 50%, 
        rgba(0, 0, 0, 0.1) 100%);
    border-radius: inherit;
    transform: translateZ(-1px);
    transition: all 0.3s ease;
}

.cta-button:hover,
.service-btn:hover,
.submit-btn:hover {
    transform: translateY(-5px) translateZ(10px);
    box-shadow: 
        0 10px 30px rgba(215, 15, 10, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 3D Navigation Effects */
.nav-container {
    transform-style: preserve-3d;
}

.nav-item a {
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
}

.nav-item a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -10px;
    right: -10px;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(215, 15, 10, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 5px;
    transform: translateZ(-1px) scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
}

.nav-item a:hover::before {
    transform: translateZ(-1px) scaleX(1);
}

/* 3D Stats Animation */
.stat-card {
    transform-style: preserve-3d;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
}

.stat-card:hover {
    transform: translateY(-10px) rotateX(10deg);
}

.stat-number {
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-number {
    transform: translateZ(20px);
    text-shadow: 0 5px 10px rgba(215, 15, 10, 0.5);
}

/* 3D Loading Animation */
.loading-content {
    transform-style: preserve-3d;
    animation: loadingFloat 3s ease-in-out infinite;
}

@keyframes loadingFloat {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg);
    }
    50% {
        transform: translateY(-20px) rotateX(5deg);
    }
}

.loading-logo {
    transform-style: preserve-3d;
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    0% {
        text-shadow: 0 0 10px rgba(215, 15, 10, 0.5);
        transform: translateZ(0px);
    }
    100% {
        text-shadow: 
            0 0 20px rgba(215, 15, 10, 0.8),
            0 0 30px rgba(215, 15, 10, 0.6),
            0 0 40px rgba(215, 15, 10, 0.4);
        transform: translateZ(10px);
    }
}

/* 3D Form Elements */
.form-group input,
.form-group select,
.form-group textarea {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 1rem;
    color: white;
    width: 100%;
    margin-bottom: 1rem;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    transform-style: preserve-3d;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--swissport-red);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px) translateZ(5px);
    box-shadow: 
        0 5px 15px rgba(215, 15, 10, 0.2),
        0 0 0 1px rgba(215, 15, 10, 0.3);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 3D Modal Effects */
.service-modal {
    backdrop-filter: blur(10px);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-content {
    transform-style: preserve-3d;
    animation: modalSlideIn 0.5s cubic-bezier(0.23, 1, 0.320, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

@keyframes modalSlideIn {
    0% {
        transform: translateY(100px) rotateX(-10deg);
        opacity: 0;
    }
    100% {
        transform: translateY(0px) rotateX(0deg);
        opacity: 1;
    }
}

/* 3D Hover Effects for Links */
.career-link {
    color: var(--swissport-red);
    text-decoration: none;
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.career-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(215, 15, 10, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 5px;
    transform: translateZ(-1px) scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
}

.career-link:hover {
    transform: translateY(-2px) translateZ(5px);
    color: white;
}

.career-link:hover::before {
    transform: translateZ(-1px) scaleX(1);
}

/* 3D Section Transitions */
section {
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

section.animate-in {
    animation: sectionSlideIn 1s cubic-bezier(0.23, 1, 0.320, 1);
}

@keyframes sectionSlideIn {
    0% {
        opacity: 0;
        transform: translateY(50px) rotateX(-5deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0px) rotateX(0deg);
    }
}

/* Responsive 3D Effects */
@media (max-width: 768px) {
    .service-card:hover {
        transform: translateY(-10px) rotateX(2deg) rotateY(2deg);
    }
    
    .stat-card:hover {
        transform: translateY(-5px) rotateX(5deg);
    }
    
    .icon-3d {
        animation-duration: 6s;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .icon-3d,
    .loading-content,
    .loading-logo {
        animation: none;
    }
    
    .service-card:hover,
    .stat-card:hover,
    .cta-button:hover,
    .service-btn:hover,
    .submit-btn:hover {
        transform: none;
    }
}
