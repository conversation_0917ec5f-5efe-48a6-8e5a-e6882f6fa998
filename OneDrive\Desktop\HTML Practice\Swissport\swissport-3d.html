<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swissport International AG - Global Aviation Excellence</title>
    <meta name="description" content="Experience Swissport's global aviation and ground handling services through our immersive 3D platform. Discover our worldwide operations, innovative solutions, and commitment to excellence.">
    <meta name="keywords" content="Swissport, aviation, ground handling, cargo, passenger services, 3D experience, global operations">
    <meta name="author" content="Swissport International AG">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Swissport International AG - Global Aviation Excellence">
    <meta property="og:description" content="Experience Swissport's global aviation services through our immersive 3D platform">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.swissport.com">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Swissport International AG - Global Aviation Excellence">
    <meta name="twitter:description" content="Experience Swissport's global aviation services through our immersive 3D platform">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --swissport-red: #d70f0a;
            --swissport-white: #ffffff;
            --swissport-black: #000000;
            --swissport-gray: #535f69;
            --swissport-light-gray: #f1f4f7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: var(--swissport-white);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--swissport-black);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-content {
            text-align: center;
        }

        .loading-logo {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--swissport-red);
            margin-bottom: 1rem;
        }

        .loading-text {
            font-size: 1.2rem;
            color: var(--swissport-white);
            margin-bottom: 2rem;
        }

        .loading-bar {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--swissport-red), #ff4444);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Navigation */
        .nav-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--swissport-red);
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-item a {
            color: var(--swissport-white);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-item a:hover {
            color: var(--swissport-red);
        }

        .nav-item a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--swissport-red);
            transition: width 0.3s ease;
        }

        .nav-item a:hover::after {
            width: 100%;
        }

        /* Hero Section */
        .hero-section {
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-content {
            text-align: center;
            z-index: 10;
            max-width: 800px;
            padding: 0 2rem;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 200;
            margin-bottom: 1rem;
            opacity: 0;
            transform: translateY(50px);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: var(--swissport-red);
            margin-bottom: 2rem;
            opacity: 0;
            transform: translateY(30px);
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0;
            transform: translateY(30px);
            color: rgba(255, 255, 255, 0.8);
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: var(--swissport-red);
            color: var(--swissport-white);
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(30px);
        }

        .cta-button:hover {
            background: #b50d08;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(215, 15, 10, 0.3);
        }

        /* 3D Globe Container */
        .globe-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        #globe-canvas {
            width: 100%;
            height: 100%;
        }

        /* Globe Info Panel */
        .globe-info {
            position: absolute;
            top: 50%;
            right: 2rem;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 2rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 300px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .globe-info.active {
            opacity: 1;
        }

        .info-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--swissport-red);
            margin-bottom: 1rem;
        }

        .info-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--swissport-red);
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .globe-info {
                position: relative;
                right: auto;
                transform: none;
                margin: 2rem;
                max-width: none;
            }
        }

        /* Scroll Indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            color: var(--swissport-white);
            text-align: center;
            opacity: 0.7;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">SWISSPORT</div>
            <div class="loading-text">Initializing 3D Experience</div>
            <div class="loading-bar">
                <div class="loading-progress" id="loadingProgress"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="nav-container">
        <div class="nav-content">
            <a href="#" class="logo">SWISSPORT</a>
            <ul class="nav-menu">
                <li class="nav-item"><a href="#home">Home</a></li>
                <li class="nav-item"><a href="#services">Services</a></li>
                <li class="nav-item"><a href="#locations">Locations</a></li>
                <li class="nav-item"><a href="#about">About</a></li>
                <li class="nav-item"><a href="#careers">Careers</a></li>
                <li class="nav-item"><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section with 3D Globe -->
    <section class="hero-section" id="home">
        <div class="globe-container">
            <canvas id="globe-canvas"></canvas>
        </div>
        
        <div class="hero-content">
            <h1 class="hero-title">Global Aviation Excellence</h1>
            <p class="hero-subtitle">Connecting the World Through Innovation</p>
            <p class="hero-description">
                Experience Swissport's worldwide network of aviation services through our interactive 3D platform. 
                Discover how we handle over 282 million passengers and 4.8 million tonnes of cargo annually.
            </p>
            <a href="#services" class="cta-button">Explore Our Services</a>
        </div>

        <div class="globe-info" id="globeInfo">
            <h3 class="info-title">Global Presence</h3>
            <div class="info-stats">
                <div class="stat-item">
                    <div class="stat-number">300+</div>
                    <div class="stat-label">Airports</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Countries</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">66K+</div>
                    <div class="stat-label">Employees</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">282M</div>
                    <div class="stat-label">Passengers</div>
                </div>
            </div>
            <p>Click on airport markers to explore our services at each location.</p>
        </div>

        <div class="scroll-indicator">
            <div>Scroll to explore</div>
            <div style="font-size: 1.5rem; margin-top: 0.5rem;">↓</div>
        </div>
    </section>

    <script src="js/globe-3d.js"></script>
    <script src="js/main.js"></script>
    <script src="js/service-showcases.js"></script>
    <script src="js/3d-navigation.js"></script>
    <script src="js/ai-avatar-chatbot.js"></script>
</body>
</html>
