/* Main CSS for Swissport 3D Website */

:root {
    --swissport-red: #d70f0a;
    --swissport-white: #ffffff;
    --swissport-black: #000000;
    --swissport-gray: #535f69;
    --swissport-light-gray: #f1f4f7;
    --swissport-dark-gray: #383e42;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--swissport-red) 0%, #b50d08 100%);
    --gradient-dark: linear-gradient(135deg, var(--swissport-black) 0%, #1a1a1a 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    
    /* Shadows */
    --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.2);
    --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
    --shadow-red: 0 10px 30px rgba(215, 15, 10, 0.3);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--gradient-dark);
    color: var(--swissport-white);
    overflow-x: hidden;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 200;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 300;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 400;
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 500;
}

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

/* Utility Classes */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.text-center {
    text-align: center;
}

.text-red {
    color: var(--swissport-red);
}

.bg-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bg-dark {
    background: rgba(0, 0, 0, 0.8);
}

/* Sections */
section {
    position: relative;
    z-index: 1;
}

.section-padding {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 200;
    margin-bottom: 1rem;
    color: var(--swissport-white);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--swissport-red);
    font-weight: 400;
}

/* Services Section */
.services-section {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    padding: 8rem 0;
    position: relative;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    position: relative;
    z-index: 2;
}

.service-card {
    background: var(--bg-glass);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    text-align: center;
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.service-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::after {
    left: 100%;
}

.service-card h3 {
    font-size: 1.5rem;
    margin: 1.5rem 0 1rem;
    color: var(--swissport-white);
}

.service-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    font-size: 1rem;
}

/* Locations Section */
.locations-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 50%, #1a1a1a 100%);
    padding: 8rem 0;
    position: relative;
}

.locations-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.stat-card {
    text-align: center;
    padding: 3rem 2rem;
    background: var(--bg-glass);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--swissport-red);
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

/* About Section */
.about-section {
    padding: 8rem 0;
    background: var(--gradient-dark);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    margin-bottom: 2rem;
}

.about-text p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
}

.about-features {
    display: grid;
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature-item {
    padding: 1.5rem;
    background: var(--bg-glass);
    border-radius: 10px;
    border-left: 4px solid var(--swissport-red);
}

.feature-item h4 {
    color: var(--swissport-red);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.feature-item p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 1rem;
}

.about-visual {
    position: relative;
    height: 500px;
    background: var(--bg-glass);
    border-radius: 15px;
    overflow: hidden;
}

.about-3d-container {
    width: 100%;
    height: 100%;
}

/* Careers Section */
.careers-section {
    padding: 8rem 0;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

.career-opportunities {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.opportunity-card {
    background: var(--bg-glass);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-smooth);
    text-align: center;
}

.opportunity-card h3 {
    color: var(--swissport-red);
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.opportunity-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.career-link {
    display: inline-block;
    padding: 0.8rem 2rem;
    background: transparent;
    color: var(--swissport-red);
    text-decoration: none;
    border: 2px solid var(--swissport-red);
    border-radius: 25px;
    transition: var(--transition-smooth);
    font-weight: 500;
}

.career-link:hover {
    background: var(--swissport-red);
    color: white;
}

/* Contact Section */
.contact-section {
    padding: 8rem 0;
    background: var(--gradient-dark);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    background: var(--bg-glass);
    padding: 2rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-item h4 {
    color: var(--swissport-red);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.contact-item p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    line-height: 1.6;
}

.contact-form {
    background: var(--bg-glass);
    padding: 2.5rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    transition: var(--transition-smooth);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--swissport-red);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(215, 15, 10, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-red);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .container {
        padding: 0 1.5rem;
    }
}

@media (max-width: 768px) {
    .services-grid,
    .locations-stats,
    .career-opportunities {
        grid-template-columns: 1fr;
    }
    
    .section-padding {
        padding: 3rem 0;
    }
    
    .services-section,
    .locations-section,
    .about-section,
    .careers-section,
    .contact-section {
        padding: 5rem 0;
    }
    
    .stat-card {
        padding: 2rem 1.5rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .service-card,
    .stat-card,
    .opportunity-card {
        padding: 2rem 1.5rem;
    }
    
    .contact-form {
        padding: 2rem 1.5rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for keyboard navigation */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--swissport-red);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --swissport-red: #ff0000;
        --swissport-white: #ffffff;
        --swissport-black: #000000;
    }
    
    .bg-glass {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
}
