// AI-Powered 3D Avatar Chatbot for Swissport
class SwissportAIAvatar {
    constructor() {
        this.isActive = false;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.avatar = null;
        this.chatHistory = [];
        this.isTyping = false;
        
        // Predefined responses for different topics
        this.responses = {
            greeting: [
                "Hello! I'm <PERSON>, your Swissport virtual assistant. How can I help you today?",
                "Welcome to Swissport! I'm here to assist you with any questions about our services.",
                "Hi there! I'm <PERSON> from Swissport. What would you like to know about our aviation services?"
            ],
            services: [
                "Swissport offers comprehensive ground handling, cargo services, passenger assistance, and aircraft fueling. Which service interests you most?",
                "We provide end-to-end aviation solutions including baggage handling, aircraft cleaning, cargo logistics, and passenger services across 300+ airports worldwide.",
                "Our services include ground handling operations, cargo and mail services, passenger services, and into-plane fueling. Would you like details about any specific service?"
            ],
            locations: [
                "Swissport operates in over 300 airports across 50 countries on 6 continents. Which location are you interested in?",
                "We have a global presence spanning Europe, Americas, Asia-Pacific, Africa, and the Middle East. I can provide information about specific airports.",
                "Our network covers major international airports worldwide. You can click on the globe to explore our locations, or tell me which airport you're interested in."
            ],
            careers: [
                "Swissport offers exciting career opportunities in ground operations, cargo handling, passenger services, and corporate roles. What type of position interests you?",
                "We're always looking for talented individuals to join our team of 66,000+ employees worldwide. Check our careers section for current openings.",
                "Career opportunities at Swissport include operational roles, management positions, and specialized technical roles. Would you like me to guide you to our careers page?"
            ],
            booking: [
                "For service bookings and inquiries, I can connect you with our sales team or direct you to the appropriate contact form. What type of service do you need?",
                "To book our services, please provide details about your requirements, and I'll guide you to the right department or contact form.",
                "I can help you get started with booking our ground handling, cargo, or passenger services. What's your specific need?"
            ],
            contact: [
                "You can reach Swissport through our global headquarters in Zurich or contact local offices directly. Would you like specific contact information?",
                "Our main office is located at The Circle 31, 8058 Zurich Airport, Switzerland. Phone: +41 43 815 00 00. How else can I help you get in touch?",
                "I can provide contact details for our global headquarters or help you find local office information. What do you prefer?"
            ],
            default: [
                "I'm here to help with information about Swissport's services, locations, careers, and contact details. Could you please rephrase your question?",
                "I can assist you with questions about our aviation services, global network, career opportunities, or how to contact us. What would you like to know?",
                "Let me help you find the information you need about Swissport. Try asking about our services, locations, careers, or contact information."
            ]
        };
        
        this.init();
    }

    init() {
        this.createChatInterface();
        this.create3DAvatar();
        this.setupEventListeners();
        this.addChatToggle();
    }

    createChatInterface() {
        const chatHTML = `
            <div id="ai-chatbot" class="ai-chatbot">
                <div class="chatbot-header">
                    <div class="avatar-container">
                        <canvas id="avatar-canvas"></canvas>
                    </div>
                    <div class="chatbot-info">
                        <h3>Sarah</h3>
                        <p>Swissport AI Assistant</p>
                        <div class="status-indicator online"></div>
                    </div>
                    <button class="close-chat" id="closeChat">&times;</button>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message bot-message">
                        <div class="message-content">
                            <p>Hello! I'm Sarah, your Swissport virtual assistant. I can help you with:</p>
                            <ul>
                                <li>🛫 Our aviation services</li>
                                <li>🌍 Global locations and airports</li>
                                <li>💼 Career opportunities</li>
                                <li>📞 Contact information</li>
                                <li>📋 Service bookings</li>
                            </ul>
                            <p>What would you like to know?</p>
                        </div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="quick-btn" data-action="services">Our Services</button>
                    <button class="quick-btn" data-action="locations">Locations</button>
                    <button class="quick-btn" data-action="careers">Careers</button>
                    <button class="quick-btn" data-action="contact">Contact</button>
                </div>
                
                <div class="chat-input-container">
                    <input type="text" id="chatInput" placeholder="Type your message..." maxlength="500">
                    <button id="sendMessage" class="send-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span class="typing-text">Sarah is typing...</span>
                </div>
            </div>
            
            <button id="chatToggle" class="chat-toggle">
                <div class="chat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                    </svg>
                </div>
                <div class="notification-badge">1</div>
            </button>
        `;

        document.body.insertAdjacentHTML('beforeend', chatHTML);
    }

    create3DAvatar() {
        const canvas = document.getElementById('avatar-canvas');
        if (!canvas) return;

        // Scene setup
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas, 
            antialias: true, 
            alpha: true 
        });
        
        this.renderer.setSize(80, 80);
        this.renderer.setPixelRatio(window.devicePixelRatio);

        // Create simple avatar
        this.createAvatarMesh();
        this.setupAvatarLighting();
        this.animateAvatar();
    }

    createAvatarMesh() {
        // Head
        const headGeometry = new THREE.SphereGeometry(0.5, 32, 32);
        const headMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xffdbac,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 0.2;

        // Hair
        const hairGeometry = new THREE.SphereGeometry(0.52, 32, 32);
        const hairMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x8B4513
        });
        const hair = new THREE.Mesh(hairGeometry, hairMaterial);
        hair.position.y = 0.3;
        hair.scale.set(1, 0.8, 1);

        // Eyes
        const eyeGeometry = new THREE.SphereGeometry(0.05, 16, 16);
        const eyeMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
        
        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.15, 0.3, 0.4);
        
        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.15, 0.3, 0.4);

        // Body
        const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.8, 32);
        const bodyMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xd70f0a // Swissport red
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = -0.4;

        // Group avatar parts
        this.avatar = new THREE.Group();
        this.avatar.add(head);
        this.avatar.add(hair);
        this.avatar.add(leftEye);
        this.avatar.add(rightEye);
        this.avatar.add(body);

        this.scene.add(this.avatar);
        this.camera.position.z = 2;
    }

    setupAvatarLighting() {
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        this.scene.add(directionalLight);
    }

    animateAvatar() {
        const animate = () => {
            requestAnimationFrame(animate);
            
            if (this.avatar) {
                // Gentle rotation and breathing animation
                this.avatar.rotation.y += 0.005;
                this.avatar.position.y = Math.sin(Date.now() * 0.001) * 0.05;
                
                // Blinking animation
                if (Math.random() < 0.01) {
                    this.blink();
                }
            }
            
            this.renderer.render(this.scene, this.camera);
        };
        animate();
    }

    blink() {
        const eyes = this.avatar.children.filter(child => 
            child.position.z > 0.3 && child.geometry instanceof THREE.SphereGeometry
        );
        
        eyes.forEach(eye => {
            gsap.to(eye.scale, {
                y: 0.1,
                duration: 0.1,
                yoyo: true,
                repeat: 1,
                ease: "power2.inOut"
            });
        });
    }

    setupEventListeners() {
        const chatToggle = document.getElementById('chatToggle');
        const closeChat = document.getElementById('closeChat');
        const sendMessage = document.getElementById('sendMessage');
        const chatInput = document.getElementById('chatInput');
        const quickBtns = document.querySelectorAll('.quick-btn');

        chatToggle.addEventListener('click', () => this.toggleChat());
        closeChat.addEventListener('click', () => this.closeChat());
        sendMessage.addEventListener('click', () => this.sendMessage());
        
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        quickBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    addChatToggle() {
        // Add pulsing animation to chat toggle
        gsap.to('#chatToggle', {
            scale: 1.1,
            duration: 1,
            repeat: -1,
            yoyo: true,
            ease: "power2.inOut"
        });
    }

    toggleChat() {
        const chatbot = document.getElementById('ai-chatbot');
        const chatToggle = document.getElementById('chatToggle');
        const badge = chatToggle.querySelector('.notification-badge');
        
        if (this.isActive) {
            this.closeChat();
        } else {
            chatbot.classList.add('active');
            chatToggle.classList.add('active');
            badge.style.display = 'none';
            this.isActive = true;
            
            // Avatar greeting animation
            this.avatarSpeak();
        }
    }

    closeChat() {
        const chatbot = document.getElementById('ai-chatbot');
        const chatToggle = document.getElementById('chatToggle');
        
        chatbot.classList.remove('active');
        chatToggle.classList.remove('active');
        this.isActive = false;
    }

    sendMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();
        
        if (!message) return;
        
        this.addMessage(message, 'user');
        input.value = '';
        
        // Show typing indicator
        this.showTyping();
        
        // Process message and respond
        setTimeout(() => {
            this.processMessage(message);
        }, 1000 + Math.random() * 2000);
    }

    handleQuickAction(action) {
        const responses = this.responses[action] || this.responses.default;
        const response = responses[Math.floor(Math.random() * responses.length)];
        
        this.showTyping();
        setTimeout(() => {
            this.addMessage(response, 'bot');
            this.avatarSpeak();
        }, 1000);
    }

    processMessage(message) {
        const lowerMessage = message.toLowerCase();
        let responseType = 'default';
        
        // Simple keyword matching
        if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
            responseType = 'greeting';
        } else if (lowerMessage.includes('service') || lowerMessage.includes('ground') || lowerMessage.includes('cargo') || lowerMessage.includes('passenger')) {
            responseType = 'services';
        } else if (lowerMessage.includes('location') || lowerMessage.includes('airport') || lowerMessage.includes('where') || lowerMessage.includes('country')) {
            responseType = 'locations';
        } else if (lowerMessage.includes('career') || lowerMessage.includes('job') || lowerMessage.includes('work') || lowerMessage.includes('employment')) {
            responseType = 'careers';
        } else if (lowerMessage.includes('book') || lowerMessage.includes('quote') || lowerMessage.includes('price') || lowerMessage.includes('cost')) {
            responseType = 'booking';
        } else if (lowerMessage.includes('contact') || lowerMessage.includes('phone') || lowerMessage.includes('email') || lowerMessage.includes('address')) {
            responseType = 'contact';
        }
        
        const responses = this.responses[responseType];
        const response = responses[Math.floor(Math.random() * responses.length)];
        
        this.hideTyping();
        this.addMessage(response, 'bot');
        this.avatarSpeak();
    }

    addMessage(content, sender) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${content}</p>
                <span class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Animate message appearance
        gsap.from(messageDiv, {
            opacity: 0,
            y: 20,
            duration: 0.3,
            ease: "power2.out"
        });
    }

    showTyping() {
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.classList.add('active');
        this.isTyping = true;
    }

    hideTyping() {
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.classList.remove('active');
        this.isTyping = false;
    }

    avatarSpeak() {
        if (!this.avatar) return;
        
        // Animate avatar when speaking
        gsap.to(this.avatar.rotation, {
            y: this.avatar.rotation.y + 0.2,
            duration: 0.5,
            ease: "power2.out"
        });
        
        gsap.to(this.avatar.scale, {
            x: 1.1,
            y: 1.1,
            z: 1.1,
            duration: 0.3,
            yoyo: true,
            repeat: 1,
            ease: "power2.inOut"
        });
    }
}

// CSS for the chatbot
const chatbotCSS = `
    .ai-chatbot {
        position: fixed;
        bottom: 100px;
        right: 20px;
        width: 350px;
        height: 500px;
        background: rgba(0, 0, 0, 0.95);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        display: flex;
        flex-direction: column;
        transform: translateY(100%) scale(0.8);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
        z-index: 10000;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    }

    .ai-chatbot.active {
        transform: translateY(0) scale(1);
        opacity: 1;
    }

    .chatbot-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        gap: 1rem;
        background: linear-gradient(135deg, var(--swissport-red), #b50d08);
        border-radius: 15px 15px 0 0;
    }

    .avatar-container {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .chatbot-info {
        flex: 1;
        position: relative;
    }

    .chatbot-info h3 {
        margin: 0;
        font-size: 1.1rem;
        color: white;
    }

    .chatbot-info p {
        margin: 0;
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .status-indicator {
        position: absolute;
        top: 0;
        right: 0;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #4CAF50;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
        100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
    }

    .close-chat {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: background 0.3s ease;
    }

    .close-chat:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .chat-messages {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .message {
        display: flex;
        max-width: 80%;
    }

    .user-message {
        align-self: flex-end;
    }

    .bot-message {
        align-self: flex-start;
    }

    .message-content {
        padding: 0.8rem 1rem;
        border-radius: 15px;
        position: relative;
    }

    .user-message .message-content {
        background: var(--swissport-red);
        color: white;
        border-bottom-right-radius: 5px;
    }

    .bot-message .message-content {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-bottom-left-radius: 5px;
    }

    .message-content ul {
        margin: 0.5rem 0;
        padding-left: 1rem;
    }

    .message-content li {
        margin: 0.3rem 0;
    }

    .message-time {
        font-size: 0.7rem;
        opacity: 0.7;
        display: block;
        margin-top: 0.3rem;
    }

    .quick-actions {
        padding: 0.5rem 1rem;
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .quick-btn {
        padding: 0.4rem 0.8rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        color: white;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .quick-btn:hover {
        background: var(--swissport-red);
        border-color: var(--swissport-red);
    }

    .chat-input-container {
        padding: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        gap: 0.5rem;
    }

    #chatInput {
        flex: 1;
        padding: 0.8rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        color: white;
        outline: none;
    }

    #chatInput::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .send-btn {
        padding: 0.8rem;
        background: var(--swissport-red);
        border: none;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .send-btn:hover {
        background: #b50d08;
        transform: scale(1.1);
    }

    .typing-indicator {
        padding: 0.5rem 1rem;
        display: none;
        align-items: center;
        gap: 0.5rem;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
    }

    .typing-indicator.active {
        display: flex;
    }

    .typing-dots {
        display: flex;
        gap: 0.2rem;
    }

    .typing-dots span {
        width: 6px;
        height: 6px;
        background: var(--swissport-red);
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
    .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .chat-toggle {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: var(--swissport-red);
        border: none;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        z-index: 9999;
        box-shadow: 0 10px 30px rgba(215, 15, 10, 0.3);
    }

    .chat-toggle:hover {
        background: #b50d08;
        transform: scale(1.1);
        box-shadow: 0 15px 40px rgba(215, 15, 10, 0.4);
    }

    .chat-toggle.active {
        background: #666;
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
        background: #ff4444;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: bold;
        color: white;
        animation: bounce 2s infinite;
    }

    @media (max-width: 768px) {
        .ai-chatbot {
            width: calc(100vw - 40px);
            height: calc(100vh - 140px);
            bottom: 80px;
            right: 20px;
            left: 20px;
        }
    }
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = chatbotCSS;
document.head.appendChild(style);

// Initialize AI Avatar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SwissportAIAvatar();
});
