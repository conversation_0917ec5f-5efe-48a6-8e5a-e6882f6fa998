// 3D Globe Implementation for Swissport
class SwissportGlobe {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.globe = null;
        this.controls = null;
        this.airports = [];
        this.markers = [];
        this.isLoaded = false;
        
        // Swissport airport locations (major hubs)
        this.airportData = [
            { name: "Zurich Airport", code: "ZUR", lat: 47.4647, lng: 8.5492, country: "Switzerland", passengers: "31.1M" },
            { name: "London Heathrow", code: "LHR", lat: 51.4700, lng: -0.4543, country: "United Kingdom", passengers: "80.9M" },
            { name: "Frankfurt Airport", code: "FRA", lat: 50.0379, lng: 8.5622, country: "Germany", passengers: "70.6M" },
            { name: "<PERSON>", code: "CDG", lat: 49.0097, lng: 2.5479, country: "France", passengers: "76.2M" },
            { name: "Amsterdam Schiphol", code: "AMS", lat: 52.3105, lng: 4.7683, country: "Netherlands", passengers: "71.7M" },
            { name: "Madrid Barajas", code: "MAD", lat: 40.4839, lng: -3.5680, country: "Spain", passengers: "61.8M" },
            { name: "Rome Fiumicino", code: "FCO", lat: 41.8003, lng: 12.2389, country: "Italy", passengers: "43.5M" },
            { name: "Vienna Airport", code: "VIE", lat: 48.1103, lng: 16.5697, country: "Austria", passengers: "31.7M" },
            { name: "Brussels Airport", code: "BRU", lat: 50.9010, lng: 4.4856, country: "Belgium", passengers: "26.4M" },
            { name: "Copenhagen Airport", code: "CPH", lat: 55.6181, lng: 12.6561, country: "Denmark", passengers: "30.3M" },
            { name: "Stockholm Arlanda", code: "ARN", lat: 59.6519, lng: 17.9186, country: "Sweden", passengers: "26.8M" },
            { name: "Oslo Airport", code: "OSL", lat: 60.1939, lng: 11.1004, country: "Norway", passengers: "28.6M" },
            { name: "Helsinki Airport", code: "HEL", lat: 60.3172, lng: 24.9633, country: "Finland", passengers: "21.9M" },
            { name: "Dubai International", code: "DXB", lat: 25.2532, lng: 55.3657, country: "UAE", passengers: "89.1M" },
            { name: "Singapore Changi", code: "SIN", lat: 1.3644, lng: 103.9915, country: "Singapore", passengers: "68.3M" },
            { name: "Hong Kong Intl", code: "HKG", lat: 22.3080, lng: 113.9185, country: "Hong Kong", passengers: "71.5M" },
            { name: "Tokyo Narita", code: "NRT", lat: 35.7720, lng: 140.3929, country: "Japan", passengers: "44.3M" },
            { name: "Sydney Airport", code: "SYD", lat: -33.9399, lng: 151.1753, country: "Australia", passengers: "44.4M" },
            { name: "JFK Airport", code: "JFK", lat: 40.6413, lng: -73.7781, country: "USA", passengers: "62.6M" },
            { name: "Los Angeles Intl", code: "LAX", lat: 33.9425, lng: -118.4081, country: "USA", passengers: "88.0M" },
            { name: "Toronto Pearson", code: "YYZ", lat: 43.6777, lng: -79.6248, country: "Canada", passengers: "50.5M" },
            { name: "São Paulo GRU", code: "GRU", lat: -23.4356, lng: -46.4731, country: "Brazil", passengers: "42.3M" }
        ];
        
        this.init();
    }

    init() {
        this.createScene();
        this.createGlobe();
        this.createAirportMarkers();
        this.setupControls();
        this.setupLighting();
        this.setupEventListeners();
        this.animate();
        this.simulateLoading();
    }

    createScene() {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000000);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 0, 5);

        // Renderer setup
        const canvas = document.getElementById('globe-canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas, 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }

    createGlobe() {
        // Globe geometry
        const geometry = new THREE.SphereGeometry(2, 64, 64);
        
        // Create earth texture (using a simple gradient for now)
        const canvas = document.createElement('canvas');
        canvas.width = 1024;
        canvas.height = 512;
        const context = canvas.getContext('2d');
        
        // Create earth-like gradient
        const gradient = context.createLinearGradient(0, 0, 0, 512);
        gradient.addColorStop(0, '#1e3c72');
        gradient.addColorStop(0.5, '#2a5298');
        gradient.addColorStop(1, '#1e3c72');
        
        context.fillStyle = gradient;
        context.fillRect(0, 0, 1024, 512);
        
        // Add some continent-like shapes
        context.fillStyle = '#2d5016';
        context.beginPath();
        context.arc(200, 200, 80, 0, Math.PI * 2);
        context.arc(600, 150, 120, 0, Math.PI * 2);
        context.arc(800, 300, 90, 0, Math.PI * 2);
        context.arc(300, 350, 70, 0, Math.PI * 2);
        context.fill();
        
        const texture = new THREE.CanvasTexture(canvas);
        
        // Globe material
        const material = new THREE.MeshPhongMaterial({
            map: texture,
            transparent: true,
            opacity: 0.9,
            shininess: 100
        });
        
        this.globe = new THREE.Mesh(geometry, material);
        this.globe.castShadow = true;
        this.globe.receiveShadow = true;
        this.scene.add(this.globe);

        // Add atmosphere glow
        const atmosphereGeometry = new THREE.SphereGeometry(2.1, 64, 64);
        const atmosphereMaterial = new THREE.MeshBasicMaterial({
            color: 0x4444ff,
            transparent: true,
            opacity: 0.1,
            side: THREE.BackSide
        });
        const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        this.scene.add(atmosphere);
    }

    createAirportMarkers() {
        this.airportData.forEach((airport, index) => {
            const marker = this.createMarker(airport);
            this.markers.push(marker);
            this.scene.add(marker);
        });
    }

    createMarker(airport) {
        // Convert lat/lng to 3D coordinates
        const phi = (90 - airport.lat) * (Math.PI / 180);
        const theta = (airport.lng + 180) * (Math.PI / 180);
        
        const x = -(2.05 * Math.sin(phi) * Math.cos(theta));
        const y = 2.05 * Math.cos(phi);
        const z = 2.05 * Math.sin(phi) * Math.sin(theta);

        // Create marker geometry
        const markerGeometry = new THREE.SphereGeometry(0.02, 8, 8);
        const markerMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xd70f0a,
            transparent: true,
            opacity: 0.9
        });
        
        const marker = new THREE.Mesh(markerGeometry, markerMaterial);
        marker.position.set(x, y, z);
        
        // Add pulsing animation
        const pulseGeometry = new THREE.RingGeometry(0.02, 0.04, 16);
        const pulseMaterial = new THREE.MeshBasicMaterial({
            color: 0xd70f0a,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide
        });
        
        const pulse = new THREE.Mesh(pulseGeometry, pulseMaterial);
        pulse.lookAt(new THREE.Vector3(0, 0, 0));
        pulse.position.copy(marker.position);
        
        // Store airport data
        marker.userData = airport;
        pulse.userData = airport;
        
        // Add to scene
        this.scene.add(pulse);
        
        // Animate pulse
        gsap.to(pulse.scale, {
            x: 2,
            y: 2,
            z: 2,
            duration: 2,
            repeat: -1,
            yoyo: true,
            ease: "power2.inOut"
        });
        
        gsap.to(pulse.material, {
            opacity: 0,
            duration: 2,
            repeat: -1,
            yoyo: true,
            ease: "power2.inOut"
        });

        return marker;
    }

    setupControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.minDistance = 3;
        this.controls.maxDistance = 10;
        this.controls.enablePan = false;
        this.controls.autoRotate = true;
        this.controls.autoRotateSpeed = 0.5;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 3, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Point light for rim lighting
        const pointLight = new THREE.PointLight(0x4444ff, 0.5, 100);
        pointLight.position.set(-5, 0, 0);
        this.scene.add(pointLight);
    }

    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Mouse interaction
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        
        this.renderer.domElement.addEventListener('click', (event) => {
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            
            raycaster.setFromCamera(mouse, this.camera);
            const intersects = raycaster.intersectObjects(this.markers);
            
            if (intersects.length > 0) {
                const airport = intersects[0].object.userData;
                this.showAirportInfo(airport);
            }
        });
    }

    showAirportInfo(airport) {
        const infoPanel = document.getElementById('globeInfo');
        infoPanel.innerHTML = `
            <h3 class="info-title">${airport.name} (${airport.code})</h3>
            <div class="info-stats">
                <div class="stat-item">
                    <div class="stat-number">${airport.passengers}</div>
                    <div class="stat-label">Annual Passengers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${airport.country}</div>
                    <div class="stat-label">Location</div>
                </div>
            </div>
            <p>Swissport provides comprehensive ground handling services at ${airport.name}, ensuring seamless operations for airlines and passengers.</p>
        `;
        infoPanel.classList.add('active');
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            infoPanel.classList.remove('active');
        }, 5000);
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        // Rotate globe slowly
        if (this.globe) {
            this.globe.rotation.y += 0.001;
        }
        
        this.renderer.render(this.scene, this.camera);
    }

    simulateLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        const loadingProgress = document.getElementById('loadingProgress');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                
                setTimeout(() => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                        this.isLoaded = true;
                        this.startIntroAnimation();
                    }, 500);
                }, 500);
            }
            
            loadingProgress.style.width = progress + '%';
        }, 100);
    }

    startIntroAnimation() {
        // Animate hero content
        gsap.timeline()
            .to('.hero-title', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' })
            .to('.hero-subtitle', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' }, '-=0.5')
            .to('.hero-description', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' }, '-=0.5')
            .to('.cta-button', { opacity: 1, y: 0, duration: 1, ease: 'power2.out' }, '-=0.5');
    }
}

// Initialize globe when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SwissportGlobe();
});
